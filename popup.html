<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Color Picker</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 Color Picker</h1>
            <div class="theme-toggle">
                <input type="checkbox" id="darkModeToggle" class="toggle-input">
                <label for="darkModeToggle" class="toggle-label">
                    <span class="toggle-inner"></span>
                    <span class="toggle-switch"></span>
                </label>
            </div>
        </div>

        <div class="main-content">
            <button id="pickColorBtn" class="pick-btn">
                <span class="btn-icon">🎯</span>
                Pick Color
            </button>

            <div class="saved-colors-section">
                <h2>Saved Colors</h2>
                <div id="savedColors" class="saved-colors-grid">
                    <!-- Saved colors will be displayed here -->
                </div>
                <div id="noColorsMsg" class="no-colors-msg">
                    No saved colors yet. Pick some colors to get started!
                </div>
            </div>
        </div>
    </div>

    <script src="popup.js"></script>
</body>
</html>