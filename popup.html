<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 300px;
      padding: 20px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      background: #f5f5f5;
    }
    
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    
    .header h1 {
      margin: 0;
      color: #333;
      font-size: 18px;
    }
    
    .pick-button {
      width: 100%;
      padding: 12px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      border-radius: 8px;
      font-size: 16px;
      cursor: pointer;
      margin-bottom: 20px;
      transition: transform 0.2s;
    }
    
    .pick-button:hover {
      transform: translateY(-2px);
    }
    
    .pick-button:disabled {
      background: #ccc;
      cursor: not-allowed;
      transform: none;
    }
    
    .current-color {
      margin-bottom: 20px;
      padding: 15px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .current-color h3 {
      margin: 0 0 10px 0;
      color: #333;
      font-size: 14px;
    }
    
    .color-display {
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .color-swatch {
      width: 40px;
      height: 40px;
      border-radius: 6px;
      border: 2px solid #ddd;
    }
    
    .color-code {
      font-family: 'Courier New', monospace;
      font-size: 16px;
      font-weight: bold;
      color: #333;
    }
    
    .saved-colors {
      background: white;
      border-radius: 8px;
      padding: 15px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .saved-colors h3 {
      margin: 0 0 15px 0;
      color: #333;
      font-size: 14px;
    }
    
    .color-list {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
      gap: 8px;
      max-height: 200px;
      overflow-y: auto;
    }
    
    .color-item {
      position: relative;
      cursor: pointer;
      border-radius: 6px;
      border: 2px solid #ddd;
      transition: transform 0.2s;
    }
    
    .color-item:hover {
      transform: scale(1.05);
      border-color: #667eea;
    }
    
    .color-item-swatch {
      width: 100%;
      height: 40px;
      border-radius: 4px;
    }
    
    .color-item-code {
      position: absolute;
      bottom: -20px;
      left: 50%;
      transform: translateX(-50%);
      font-size: 10px;
      font-family: 'Courier New', monospace;
      color: #666;
      white-space: nowrap;
    }
    
    .no-colors {
      text-align: center;
      color: #999;
      font-style: italic;
      padding: 20px;
    }
    
    .status {
      margin-top: 10px;
      padding: 8px;
      border-radius: 4px;
      font-size: 12px;
      text-align: center;
    }
    
    .status.success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    
    .status.error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>🎨 Color Picker</h1>
  </div>
  
  <button id="pickColorBtn" class="pick-button">
    Pick Color from Page
  </button>
  
  <div id="currentColor" class="current-color" style="display: none;">
    <h3>Picked Color:</h3>
    <div class="color-display">
      <div id="currentSwatch" class="color-swatch"></div>
      <div id="currentCode" class="color-code"></div>
    </div>
  </div>
  
  <div class="saved-colors">
    <h3>Saved Colors</h3>
    <div id="colorList" class="color-list">
      <div class="no-colors">No colors saved yet</div>
    </div>
  </div>
  
  <div id="status" class="status" style="display: none;"></div>
  
  <script src="popup.js"></script>
</body>
</html>
