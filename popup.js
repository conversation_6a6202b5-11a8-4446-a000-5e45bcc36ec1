// DOM elements
const pickColorBtn = document.getElementById('pickColorBtn');
const currentColorDiv = document.getElementById('currentColor');
const currentSwatch = document.getElementById('currentSwatch');
const currentCode = document.getElementById('currentCode');
const colorList = document.getElementById('colorList');
const statusDiv = document.getElementById('status');

// Initialize popup
document.addEventListener('DOMContentLoaded', () => {
  loadSavedColors();
  
  // Add click handler for pick color button
  pickColorBtn.addEventListener('click', pickColor);
});

// Pick color function
async function pickColor() {
  try {
    // Disable button during picking
    pickColorBtn.disabled = true;
    pickColorBtn.textContent = 'Picking Color...';
    
    // Get active tab
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    
    if (!tab) {
      throw new Error('No active tab found');
    }
    
    // Send message to content script
    const response = await chrome.tabs.sendMessage(tab.id, { action: 'pickColor' });
    
    if (response && response.success && response.color) {
      // Display the picked color
      displayCurrentColor(response.color);
      
      // Save to localStorage
      await saveColor(response.color);
      
      // Reload saved colors display
      loadSavedColors();
      
      // Show success status
      showStatus('Color picked and saved!', 'success');
    } else if (response && response.cancelled) {
      showStatus('Color picking cancelled', 'error');
    } else {
      throw new Error(response?.error || 'Failed to pick color');
    }
    
  } catch (error) {
    console.error('Error picking color:', error);
    showStatus(`Error: ${error.message}`, 'error');
  } finally {
    // Re-enable button
    pickColorBtn.disabled = false;
    pickColorBtn.textContent = 'Pick Color from Page';
  }
}

// Display current picked color
function displayCurrentColor(color) {
  currentColorDiv.style.display = 'block';
  currentSwatch.style.backgroundColor = color;
  currentCode.textContent = color.toUpperCase();
}

// Save color to localStorage
async function saveColor(color) {
  try {
    // Get existing colors
    const result = await chrome.storage.local.get(['savedColors']);
    let savedColors = result.savedColors || [];
    
    // Check if color already exists (avoid duplicates)
    const colorExists = savedColors.some(savedColor => 
      savedColor.toLowerCase() === color.toLowerCase()
    );
    
    if (!colorExists) {
      // Add new color to the beginning of the array
      savedColors.unshift(color.toUpperCase());
      
      // Limit to 50 colors to prevent storage bloat
      if (savedColors.length > 50) {
        savedColors = savedColors.slice(0, 50);
      }
      
      // Save back to storage
      await chrome.storage.local.set({ savedColors });
    }
  } catch (error) {
    console.error('Error saving color:', error);
  }
}

// Load and display saved colors
async function loadSavedColors() {
  try {
    const result = await chrome.storage.local.get(['savedColors']);
    const savedColors = result.savedColors || [];
    
    if (savedColors.length === 0) {
      colorList.innerHTML = '<div class="no-colors">No colors saved yet</div>';
      return;
    }
    
    // Create color items
    colorList.innerHTML = savedColors.map(color => `
      <div class="color-item" data-color="${color}" title="Click to copy ${color}">
        <div class="color-item-swatch" style="background-color: ${color}"></div>
        <div class="color-item-code">${color}</div>
      </div>
    `).join('');
    
    // Add click handlers for copying colors
    colorList.querySelectorAll('.color-item').forEach(item => {
      item.addEventListener('click', () => {
        const color = item.dataset.color;
        copyToClipboard(color);
        showStatus(`Copied ${color} to clipboard!`, 'success');
      });
    });
    
  } catch (error) {
    console.error('Error loading saved colors:', error);
    colorList.innerHTML = '<div class="no-colors">Error loading colors</div>';
  }
}

// Copy text to clipboard
async function copyToClipboard(text) {
  try {
    await navigator.clipboard.writeText(text);
  } catch (error) {
    console.error('Error copying to clipboard:', error);
    // Fallback for older browsers
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
  }
}

// Show status message
function showStatus(message, type) {
  statusDiv.textContent = message;
  statusDiv.className = `status ${type}`;
  statusDiv.style.display = 'block';
  
  // Hide after 3 seconds
  setTimeout(() => {
    statusDiv.style.display = 'none';
  }, 3000);
}

// Listen for messages from content script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'colorPicked' && message.color) {
    displayCurrentColor(message.color);
    saveColor(message.color);
    loadSavedColors();
    showStatus('Color picked and saved!', 'success');
  }
});
