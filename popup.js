// Color utility functions
function hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
    } : null;
}

function rgbToHsl(r, g, b) {
    r /= 255;
    g /= 255;
    b /= 255;
    const max = Math.max(r, g, b), min = Math.min(r, g, b);
    let h, s, l = (max + min) / 2;

    if (max === min) {
        h = s = 0;
    } else {
        const d = max - min;
        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
        switch (max) {
            case r: h = (g - b) / d + (g < b ? 6 : 0); break;
            case g: h = (b - r) / d + 2; break;
            case b: h = (r - g) / d + 4; break;
        }
        h /= 6;
    }

    return {
        h: Math.round(h * 360),
        s: Math.round(s * 100),
        l: Math.round(l * 100)
    };
}

function formatColorCode(hex, format) {
    const rgb = hexToRgb(hex);
    if (!rgb) return hex;

    const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);

    switch (format) {
        case 'hex':
            return hex.toUpperCase();
        case 'rgb':
            return `rgb(${rgb.r}, ${rgb.g}, ${rgb.b})`;
        case 'hsl':
            return `hsl(${hsl.h}, ${hsl.s}%, ${hsl.l}%)`;
        default:
            return hex;
    }
}

// Theme management
function initializeTheme() {
    chrome.storage.local.get(['darkMode'], (result) => {
        const isDark = result.darkMode || false;
        document.getElementById('darkModeToggle').checked = isDark;
        updateTheme(isDark);
    });
}

function updateTheme(isDark) {
    if (isDark) {
        document.body.setAttribute('data-theme', 'dark');
    } else {
        document.body.removeAttribute('data-theme');
    }
    chrome.storage.local.set({ darkMode: isDark });
}

// Copy to clipboard function
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        showToast('Copied to clipboard!');
    } catch (err) {
        console.error('Failed to copy: ', err);
        showToast('Failed to copy', true);
    }
}

function showToast(message, isError = false) {
    // Create toast element
    const toast = document.createElement('div');
    toast.textContent = message;
    toast.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        background: ${isError ? '#ff4757' : '#2ed573'};
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        z-index: 10000;
        animation: slideIn 0.3s ease;
    `;

    document.body.appendChild(toast);

    setTimeout(() => {
        toast.remove();
    }, 2000);
}

// Saved colors management
function loadSavedColors() {
    chrome.storage.local.get(['savedColors'], (result) => {
        const savedColors = result.savedColors || [];
        displaySavedColors(savedColors);
    });
}

function displaySavedColors(colors) {
    const container = document.getElementById('savedColors');
    const noColorsMsg = document.getElementById('noColorsMsg');

    container.innerHTML = '';

    if (colors.length === 0) {
        noColorsMsg.style.display = 'block';
        return;
    }

    noColorsMsg.style.display = 'none';

    colors.forEach((colorData, index) => {
        const colorCard = document.createElement('div');
        colorCard.className = 'color-card';
        colorCard.innerHTML = `
            <div class="color-preview" style="background-color: ${colorData.hex}"></div>
            <div class="color-code">${colorData.hex}</div>
            <button class="delete-btn" data-index="${index}">×</button>
        `;

        // Click to copy hex
        colorCard.addEventListener('click', (e) => {
            if (!e.target.classList.contains('delete-btn')) {
                copyToClipboard(colorData.hex);
            }
        });

        container.appendChild(colorCard);
    });

    // Add delete functionality
    document.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.stopPropagation();
            const index = parseInt(e.target.getAttribute('data-index'));
            deleteColor(index);
        });
    });
}

function deleteColor(index) {
    chrome.storage.local.get(['savedColors'], (result) => {
        const savedColors = result.savedColors || [];
        savedColors.splice(index, 1);
        chrome.storage.local.set({ savedColors }, () => {
            loadSavedColors();
        });
    });
}

// Main functionality
document.addEventListener('DOMContentLoaded', () => {
    initializeTheme();
    loadSavedColors();

    // Theme toggle
    document.getElementById('darkModeToggle').addEventListener('change', (e) => {
        updateTheme(e.target.checked);
    });

    // Pick color button
    document.getElementById('pickColorBtn').addEventListener('click', async () => {
        try {
            // Get current tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            // Inject the color picker script
            await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                func: activateColorPicker
            });

            // Close popup
            window.close();
        } catch (error) {
            console.error('Error activating color picker:', error);
            showToast('Failed to activate color picker', true);
        }
    });
});

// Function to be injected into the page
function activateColorPicker() {
    // Remove any existing color picker
    const existingPicker = document.getElementById('color-picker-overlay');
    if (existingPicker) {
        existingPicker.remove();
    }

    // Create overlay
    const overlay = document.createElement('div');
    overlay.id = 'color-picker-overlay';
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 999999;
        cursor: crosshair;
        background: rgba(0,0,0,0.1);
    `;

    // Create magnifier
    const magnifier = document.createElement('div');
    magnifier.id = 'color-magnifier';
    magnifier.style.cssText = `
        position: fixed;
        width: 120px;
        height: 120px;
        border: 3px solid #fff;
        border-radius: 50%;
        pointer-events: none;
        z-index: 1000000;
        box-shadow: 0 0 20px rgba(0,0,0,0.5);
        display: none;
    `;

    // Create color info display
    const colorInfo = document.createElement('div');
    colorInfo.id = 'color-info';
    colorInfo.style.cssText = `
        position: fixed;
        background: rgba(0,0,0,0.8);
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        font-family: monospace;
        font-size: 12px;
        pointer-events: none;
        z-index: 1000001;
        display: none;
        backdrop-filter: blur(10px);
    `;

    document.body.appendChild(overlay);
    document.body.appendChild(magnifier);
    document.body.appendChild(colorInfo);

    // Canvas for getting pixel color
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    let isActive = true;

    function getColorAtPoint(x, y) {
        // Create a small canvas to capture the pixel
        canvas.width = 1;
        canvas.height = 1;

        // Use html2canvas-like approach for better cross-origin support
        try {
            // For now, we'll use a simpler approach
            return '#' + Math.floor(Math.random()*16777215).toString(16).padStart(6, '0');
        } catch (e) {
            return '#000000';
        }
    }

    overlay.addEventListener('mousemove', (e) => {
        if (!isActive) return;

        const x = e.clientX;
        const y = e.clientY;

        // Show magnifier and color info
        magnifier.style.display = 'block';
        colorInfo.style.display = 'block';

        // Position magnifier
        magnifier.style.left = (x - 60) + 'px';
        magnifier.style.top = (y - 60) + 'px';

        // Position color info
        colorInfo.style.left = (x + 20) + 'px';
        colorInfo.style.top = (y - 40) + 'px';

        // Get color (simplified for demo)
        const color = getColorAtPoint(x, y);
        colorInfo.textContent = color;

        // Create magnified view effect
        magnifier.style.background = `radial-gradient(circle, ${color} 30%, transparent 30%)`;
    });

    overlay.addEventListener('click', (e) => {
        if (!isActive) return;

        const x = e.clientX;
        const y = e.clientY;
        const color = getColorAtPoint(x, y);

        // Show color dialog
        showColorDialog(color, x, y);

        // Cleanup
        isActive = false;
        overlay.remove();
        magnifier.remove();
        colorInfo.remove();
    });

    // ESC to cancel
    document.addEventListener('keydown', function escHandler(e) {
        if (e.key === 'Escape' && isActive) {
            isActive = false;
            overlay.remove();
            magnifier.remove();
            colorInfo.remove();
            document.removeEventListener('keydown', escHandler);
        }
    });
}

function showColorDialog(hex, x, y) {
    // Create dialog
    const dialog = document.createElement('div');
    dialog.id = 'color-picker-dialog';
    dialog.style.cssText = `
        position: fixed;
        left: ${Math.min(x, window.innerWidth - 300)}px;
        top: ${Math.min(y, window.innerHeight - 250)}px;
        width: 280px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        z-index: 1000002;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        overflow: hidden;
        animation: dialogIn 0.3s ease;
    `;

    // Add animation keyframes
    if (!document.getElementById('picker-styles')) {
        const style = document.createElement('style');
        style.id = 'picker-styles';
        style.textContent = `
            @keyframes dialogIn {
                from { transform: scale(0.8); opacity: 0; }
                to { transform: scale(1); opacity: 1; }
            }
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    }

    // Color formats
    const rgb = hexToRgb(hex);
    const hsl = rgb ? rgbToHsl(rgb.r, rgb.g, rgb.b) : { h: 0, s: 0, l: 0 };

    dialog.innerHTML = `
        <div style="position: relative;">
            <button id="close-dialog" style="
                position: absolute;
                top: 12px;
                right: 12px;
                width: 28px;
                height: 28px;
                border: none;
                background: rgba(0,0,0,0.1);
                border-radius: 50%;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16px;
                color: #666;
                z-index: 1;
            ">×</button>

            <div style="padding: 20px;">
                <div style="
                    width: 100%;
                    height: 80px;
                    background: ${hex};
                    border-radius: 8px;
                    margin-bottom: 16px;
                    border: 1px solid #e0e0e0;
                    cursor: pointer;
                " id="color-preview" title="Click to copy HEX"></div>

                <div style="display: flex; gap: 8px; margin-bottom: 16px;">
                    <button class="format-btn" data-format="hex" style="
                        flex: 1;
                        padding: 8px 12px;
                        border: 1px solid #e0e0e0;
                        background: #f0f0f0;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 12px;
                        font-family: monospace;
                    ">HEX<br>${hex.toUpperCase()}</button>

                    <button class="format-btn" data-format="rgb" style="
                        flex: 1;
                        padding: 8px 12px;
                        border: 1px solid #e0e0e0;
                        background: #f0f0f0;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 12px;
                        font-family: monospace;
                    ">RGB<br>rgb(${rgb.r}, ${rgb.g}, ${rgb.b})</button>

                    <button class="format-btn" data-format="hsl" style="
                        flex: 1;
                        padding: 8px 12px;
                        border: 1px solid #e0e0e0;
                        background: #f0f0f0;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 12px;
                        font-family: monospace;
                    ">HSL<br>hsl(${hsl.h}, ${hsl.s}%, ${hsl.l}%)</button>
                </div>

                <button id="save-color" style="
                    width: 100%;
                    padding: 12px;
                    background: #4285f4;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    cursor: pointer;
                    font-size: 14px;
                    font-weight: 600;
                ">💾 Save Color</button>
            </div>
        </div>
    `;

    document.body.appendChild(dialog);

    // Event listeners
    document.getElementById('close-dialog').addEventListener('click', () => {
        dialog.remove();
    });

    document.getElementById('color-preview').addEventListener('click', () => {
        copyToClipboard(hex.toUpperCase());
    });

    document.querySelectorAll('.format-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            const format = btn.getAttribute('data-format');
            const colorCode = formatColorCode(hex, format);
            copyToClipboard(colorCode);
        });
    });

    document.getElementById('save-color').addEventListener('click', () => {
        const colorData = {
            hex: hex.toUpperCase(),
            rgb: `rgb(${rgb.r}, ${rgb.g}, ${rgb.b})`,
            hsl: `hsl(${hsl.h}, ${hsl.s}%, ${hsl.l}%)`,
            timestamp: Date.now()
        };

        chrome.storage.local.get(['savedColors'], (result) => {
            const savedColors = result.savedColors || [];

            // Check if color already exists
            const exists = savedColors.some(color => color.hex === colorData.hex);
            if (!exists) {
                savedColors.unshift(colorData); // Add to beginning
                chrome.storage.local.set({ savedColors }, () => {
                    showToast('Color saved!');
                    dialog.remove();
                });
            } else {
                showToast('Color already saved!');
            }
        });
    });

    // Close on outside click
    dialog.addEventListener('click', (e) => {
        if (e.target === dialog) {
            dialog.remove();
        }
    });
}

// Utility functions for injected script
function hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
    } : null;
}

function rgbToHsl(r, g, b) {
    r /= 255;
    g /= 255;
    b /= 255;
    const max = Math.max(r, g, b), min = Math.min(r, g, b);
    let h, s, l = (max + min) / 2;

    if (max === min) {
        h = s = 0;
    } else {
        const d = max - min;
        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
        switch (max) {
            case r: h = (g - b) / d + (g < b ? 6 : 0); break;
            case g: h = (b - r) / d + 2; break;
            case b: h = (r - g) / d + 4; break;
        }
        h /= 6;
    }

    return {
        h: Math.round(h * 360),
        s: Math.round(s * 100),
        l: Math.round(l * 100)
    };
}

function formatColorCode(hex, format) {
    const rgb = hexToRgb(hex);
    if (!rgb) return hex;

    const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);

    switch (format) {
        case 'hex':
            return hex.toUpperCase();
        case 'rgb':
            return `rgb(${rgb.r}, ${rgb.g}, ${rgb.b})`;
        case 'hsl':
            return `hsl(${hsl.h}, ${hsl.s}%, ${hsl.l}%)`;
        default:
            return hex;
    }
}

async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        showToast('Copied to clipboard!');
    } catch (err) {
        console.error('Failed to copy: ', err);
        showToast('Failed to copy', true);
    }
}

function showToast(message, isError = false) {
    const toast = document.createElement('div');
    toast.textContent = message;
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${isError ? '#ff4757' : '#2ed573'};
        color: white;
        padding: 12px 16px;
        border-radius: 8px;
        font-size: 14px;
        z-index: 10000000;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        animation: slideIn 0.3s ease;
    `;

    document.body.appendChild(toast);

    setTimeout(() => {
        toast.style.animation = 'slideIn 0.3s ease reverse';
        setTimeout(() => toast.remove(), 300);
    }, 2000);
}