/* Color Picker Overlay Styles */
.color-picker-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    z-index: 999999 !important;
    cursor: crosshair !important;
    background: rgba(0, 0, 0, 0.05) !important;
    backdrop-filter: blur(1px) !important;
}

/* Magnifier Styles */
.color-magnifier {
    position: fixed !important;
    width: 150px !important;
    height: 150px !important;
    border: 4px solid #ffffff !important;
    border-radius: 50% !important;
    pointer-events: none !important;
    z-index: 1000000 !important;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.3), 
                0 0 0 2px rgba(0, 0, 0, 0.1) !important;
    background: var(--current-color, #000000) !important;
    backdrop-filter: blur(0px) !important;
    transition: all 0.1s ease !important;
}

.magnifier-crosshair {
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    width: 20px !important;
    height: 20px !important;
    transform: translate(-50%, -50%) !important;
    border: 2px solid rgba(255, 255, 255, 0.8) !important;
    border-radius: 50% !important;
    background: rgba(0, 0, 0, 0.1) !important;
}

.magnifier-crosshair::before,
.magnifier-crosshair::after {
    content: '' !important;
    position: absolute !important;
    background: rgba(255, 255, 255, 0.8) !important;
}

.magnifier-crosshair::before {
    top: 50% !important;
    left: -10px !important;
    right: -10px !important;
    height: 1px !important;
    transform: translateY(-50%) !important;
}

.magnifier-crosshair::after {
    left: 50% !important;
    top: -10px !important;
    bottom: -10px !important;
    width: 1px !important;
    transform: translateX(-50%) !important;
}

/* Color Tooltip Styles */
.color-tooltip {
    position: fixed !important;
    background: rgba(0, 0, 0, 0.9) !important;
    color: white !important;
    padding: 8px 12px !important;
    border-radius: 8px !important;
    font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, sans-serif !important;
    font-size: 12px !important;
    font-weight: 600 !important;
    pointer-events: none !important;
    z-index: 1000001 !important;
    backdrop-filter: blur(10px) !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3) !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    min-width: 100px !important;
}

.tooltip-color {
    width: 20px !important;
    height: 20px !important;
    border-radius: 4px !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
    flex-shrink: 0 !important;
}

.tooltip-text {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
    letter-spacing: 0.5px !important;
}

/* Color Dialog Styles */
.color-dialog {
    position: fixed !important;
    width: 300px !important;
    background: #ffffff !important;
    border-radius: 16px !important;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25) !important;
    z-index: 1000002 !important;
    font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, sans-serif !important;
    overflow: hidden !important;
    animation: dialogSlideIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
    backdrop-filter: blur(10px) !important;
}

.dialog-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 20px 20px 16px !important;
    border-bottom: 1px solid #f0f0f0 !important;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;
}

.dialog-header h3 {
    margin: 0 !important;
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #333333 !important;
}

.close-btn {
    width: 32px !important;
    height: 32px !important;
    border: none !important;
    background: rgba(0, 0, 0, 0.05) !important;
    border-radius: 50% !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 18px !important;
    color: #666666 !important;
    transition: all 0.2s ease !important;
}

.close-btn:hover {
    background: rgba(255, 0, 0, 0.1) !important;
    color: #ff4757 !important;
    transform: scale(1.1) !important;
}

.dialog-content {
    padding: 20px !important;
}

.color-preview-large {
    width: 100% !important;
    height: 100px !important;
    border-radius: 12px !important;
    margin-bottom: 20px !important;
    border: 3px solid #f0f0f0 !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    position: relative !important;
    overflow: hidden !important;
}

.color-preview-large::before {
    content: 'Click to copy HEX' !important;
    position: absolute !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    background: rgba(0, 0, 0, 0.7) !important;
    color: white !important;
    padding: 8px !important;
    text-align: center !important;
    font-size: 12px !important;
    opacity: 0 !important;
    transition: opacity 0.2s ease !important;
}

.color-preview-large:hover::before {
    opacity: 1 !important;
}

.color-preview-large:hover {
    transform: scale(1.02) !important;
    border-color: #4285f4 !important;
}

.color-formats {
    display: flex !important;
    gap: 10px !important;
    margin-bottom: 20px !important;
}

.format-btn {
    flex: 1 !important;
    padding: 12px 8px !important;
    border: 2px solid #e8eaed !important;
    background: #f8f9fa !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    font-size: 11px !important;
    transition: all 0.2s ease !important;
    text-align: center !important;
}

.format-btn:hover {
    border-color: #4285f4 !important;
    background: #e8f0fe !important;
    transform: translateY(-2px) !important;
}

.format-label {
    display: block !important;
    font-weight: 600 !important;
    color: #5f6368 !important;
    margin-bottom: 4px !important;
    font-size: 10px !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.format-value {
    display: block !important;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
    color: #202124 !important;
    font-size: 10px !important;
    font-weight: 500 !important;
    word-break: break-all !important;
}

.save-btn {
    width: 100% !important;
    padding: 14px !important;
    background: linear-gradient(135deg, #4285f4, #1a73e8) !important;
    color: white !important;
    border: none !important;
    border-radius: 10px !important;
    cursor: pointer !important;
    font-size: 15px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 8px rgba(66, 133, 244, 0.3) !important;
}

.save-btn:hover {
    background: linear-gradient(135deg, #1a73e8, #1557b0) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 16px rgba(66, 133, 244, 0.4) !important;
}

.save-btn:active {
    transform: translateY(0) !important;
}

/* Toast Styles */
.color-picker-toast {
    font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, sans-serif !important;
    font-weight: 500 !important;
}

/* Animations */
@keyframes dialogSlideIn {
    from {
        transform: scale(0.8) translateY(-20px);
        opacity: 0;
    }
    to {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .color-dialog {
        background: #2d2d2d !important;
        color: #ffffff !important;
    }

    .dialog-header {
        background: linear-gradient(135deg, #3c3c3c 0%, #2d2d2d 100%) !important;
        border-bottom-color: #404040 !important;
    }

    .dialog-header h3 {
        color: #ffffff !important;
    }

    .format-btn {
        background: #3c3c3c !important;
        border-color: #404040 !important;
        color: #ffffff !important;
    }

    .format-btn:hover {
        background: #404040 !important;
        border-color: #5f7de8 !important;
    }

    .format-label {
        color: #b0b0b0 !important;
    }

    .format-value {
        color: #ffffff !important;
    }

    .color-preview-large {
        border-color: #404040 !important;
    }

    .color-preview-large:hover {
        border-color: #5f7de8 !important;
    }
}

/* Ensure proper stacking and isolation */
.color-picker-overlay,
.color-magnifier,
.color-tooltip,
.color-dialog {
    isolation: isolate !important;
}