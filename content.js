// Content script for color picker functionality

// Check if EyeDropper API is supported
const isEyeDropperSupported = 'EyeDropper' in window;

// Listen for messages from popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'pickColor') {
    handleColorPick(sendResponse);
    return true; // Keep message channel open for async response
  }
});

// Handle color picking
async function handleColorPick(sendResponse) {
  try {
    // Check if EyeDropper is supported
    if (!isEyeDropperSupported) {
      sendResponse({
        success: false,
        error: 'EyeDropper API is not supported in this browser. Please use Chrome 95+ or Edge 95+.'
      });
      return;
    }

    // Create EyeDropper instance
    const eyeDropper = new EyeDropper();
    
    // Open the eye dropper
    const result = await eyeDropper.open();
    
    if (result && result.sRGBHex) {
      const color = result.sRGBHex;
      
      // Copy color to clipboard
      await copyColorToClipboard(color);
      
      // Send color back to popup
      sendResponse({
        success: true,
        color: color
      });
      
      // Also send message to popup (alternative method)
      chrome.runtime.sendMessage({
        action: 'colorPicked',
        color: color
      });
      
      // Show temporary notification on page
      showColorNotification(color);
      
    } else {
      sendResponse({
        success: false,
        cancelled: true,
        error: 'Color picking was cancelled'
      });
    }
    
  } catch (error) {
    console.error('Error in color picking:', error);
    
    // Handle specific error cases
    let errorMessage = 'Failed to pick color';
    
    if (error.name === 'AbortError') {
      errorMessage = 'Color picking was cancelled';
      sendResponse({
        success: false,
        cancelled: true,
        error: errorMessage
      });
    } else if (error.name === 'NotAllowedError') {
      errorMessage = 'Permission denied. Please allow the extension to access the page.';
      sendResponse({
        success: false,
        error: errorMessage
      });
    } else {
      sendResponse({
        success: false,
        error: errorMessage + ': ' + error.message
      });
    }
  }
}

// Copy color to clipboard
async function copyColorToClipboard(color) {
  try {
    await navigator.clipboard.writeText(color);
    console.log(`Color ${color} copied to clipboard`);
  } catch (error) {
    console.error('Error copying to clipboard:', error);
    
    // Fallback method for clipboard access
    try {
      const textArea = document.createElement('textarea');
      textArea.value = color;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      console.log(`Color ${color} copied to clipboard (fallback method)`);
    } catch (fallbackError) {
      console.error('Fallback clipboard copy also failed:', fallbackError);
    }
  }
}

// Show temporary notification on the page
function showColorNotification(color) {
  // Remove any existing notification
  const existingNotification = document.getElementById('color-picker-notification');
  if (existingNotification) {
    existingNotification.remove();
  }
  
  // Create notification element
  const notification = document.createElement('div');
  notification.id = 'color-picker-notification';
  notification.innerHTML = `
    <div style="
      position: fixed;
      top: 20px;
      right: 20px;
      background: white;
      border: 2px solid ${color};
      border-radius: 8px;
      padding: 15px 20px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 10000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      color: #333;
      display: flex;
      align-items: center;
      gap: 10px;
      animation: slideIn 0.3s ease-out;
    ">
      <div style="
        width: 20px;
        height: 20px;
        background: ${color};
        border-radius: 4px;
        border: 1px solid #ddd;
      "></div>
      <div>
        <div style="font-weight: bold;">Color Picked!</div>
        <div style="font-family: monospace; font-size: 12px; color: #666;">${color}</div>
      </div>
    </div>
  `;
  
  // Add CSS animation
  const style = document.createElement('style');
  style.textContent = `
    @keyframes slideIn {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }
  `;
  document.head.appendChild(style);
  
  // Add to page
  document.body.appendChild(notification);
  
  // Remove after 3 seconds
  setTimeout(() => {
    if (notification && notification.parentNode) {
      notification.style.animation = 'slideIn 0.3s ease-out reverse';
      setTimeout(() => {
        if (notification && notification.parentNode) {
          notification.remove();
        }
        if (style && style.parentNode) {
          style.remove();
        }
      }, 300);
    }
  }, 3000);
}

// Log that content script is loaded
console.log('Color Picker content script loaded. EyeDropper supported:', isEyeDropperSupported);
