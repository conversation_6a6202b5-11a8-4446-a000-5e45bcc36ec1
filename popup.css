* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --bg-color: #ffffff;
    --text-color: #333333;
    --primary-color: #4285f4;
    --primary-hover: #3367d6;
    --border-color: #e0e0e0;
    --card-bg: #f8f9fa;
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
    --border-radius: 8px;
}

[data-theme="dark"] {
    --bg-color: #1a1a1a;
    --text-color: #ffffff;
    --primary-color: #5f7de8;
    --primary-hover: #4a6bc7;
    --border-color: #404040;
    --card-bg: #2d2d2d;
    --shadow: 0 2px 10px rgba(0,0,0,0.3);
}

body {
    width: 320px;
    min-height: 400px;
    background-color: var(--bg-color);
    color: var(--text-color);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON>o, sans-serif;
    transition: all 0.3s ease;
}

.container {
    padding: 16px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--border-color);
}

.header h1 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
}

.theme-toggle {
    position: relative;
}

.toggle-input {
    display: none;
}

.toggle-label {
    display: block;
    width: 44px;
    height: 24px;
    background-color: var(--border-color);
    border-radius: 12px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    position: relative;
}

.toggle-input:checked + .toggle-label {
    background-color: var(--primary-color);
}

.toggle-switch {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background-color: white;
    border-radius: 50%;
    transition: transform 0.3s ease;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.toggle-input:checked + .toggle-label .toggle-switch {
    transform: translateX(20px);
}

.pick-btn {
    width: 100%;
    padding: 14px 20px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 24px;
}

.pick-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.pick-btn:active {
    transform: translateY(0);
}

.btn-icon {
    font-size: 18px;
}

.saved-colors-section h2 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
    color: var(--text-color);
}

.saved-colors-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 8px;
    max-height: 200px;
    overflow-y: auto;
}

.color-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.color-card:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.color-preview {
    width: 100%;
    height: 40px;
    border-radius: 4px;
    margin-bottom: 6px;
    border: 1px solid var(--border-color);
}

.color-code {
    font-size: 10px;
    text-align: center;
    color: var(--text-color);
    font-family: monospace;
    word-break: break-all;
}

.delete-btn {
    position: absolute;
    top: -4px;
    right: -4px;
    width: 18px;
    height: 18px;
    background: #ff4757;
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.color-card:hover .delete-btn {
    opacity: 1;
}

.no-colors-msg {
    text-align: center;
    color: var(--text-color);
    opacity: 0.6;
    font-size: 14px;
    padding: 20px;
    font-style: italic;
}

.saved-colors-grid::-webkit-scrollbar {
    width: 6px;
}

.saved-colors-grid::-webkit-scrollbar-track {
    background: var(--card-bg);
    border-radius: 3px;
}

.saved-colors-grid::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.saved-colors-grid::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}