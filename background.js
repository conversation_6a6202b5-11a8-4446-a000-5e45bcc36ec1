// Background service worker for Chrome extension
chrome.runtime.onInstalled.addListener(() => {
    console.log('Color Picker Extension installed');
});

// Handle messages from popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'activateColorPicker') {
        // Forward message to content script
        chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
            if (tabs[0]) {
                chrome.tabs.sendMessage(tabs[0].id, request, sendResponse);
            }
        });
        return true; // Keep message channel open for async response
    }
});

console.log('chrome:', chrome);
console.log('chrome.contextMenus:', chrome.contextMenus);

// Context menu integration (optional enhancement)
chrome.contextMenus.create({
    id: 'pickColor',
    title: 'Pick Color',
    contexts: ['all']
});

chrome.contextMenus.onClicked.addListener((info, tab) => {
    if (info.menuItemId === 'pickColor') {
        // Inject and activate color picker
        chrome.scripting.executeScript({
            target: { tabId: tab.id },
            func: () => {
                // Send message to content script to activate picker
                window.postMessage({ action: 'activateColorPicker', source: 'colorPickerExtension' }, '*');
            }
        });
    }
});