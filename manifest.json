{"manifest_version": 3, "name": "Color Picker Extension", "version": "1.0", "description": "Pick colors from any webpage using the EyeDropper API", "permissions": ["activeTab", "storage"], "action": {"default_popup": "popup.html", "default_title": "Color Picker", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.js"], "run_at": "document_idle"}], "icons": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}