# Advanced Color Picker Chrome Extension

A powerful and beautiful Chrome extension for picking colors from any webpage with advanced features and an intuitive UI.

## ✨ Features

### 🎯 Core Functionality
- **One-click color picking**: Click the extension button and instantly pick colors
- **Real-time color preview**: See colors as you hover with a magnifying glass
- **Multiple format support**: Get colors in HEX, RGB, and HSL formats
- **Instant clipboard copying**: One-click copy in any format

### 🎨 Advanced Features
- **Color history**: Save and manage your picked colors
- **Dark mode support**: Beautiful dark theme with toggle switch
- **Smart color detection**: Advanced algorithms for accurate color detection
- **Responsive design**: Works perfectly on any screen size

### 🚀 User Experience
- **Smooth animations**: Polished transitions and micro-interactions
- **Intuitive interface**: Clean, modern design following Material Design principles
- **Keyboard shortcuts**: ESC to cancel color picking
- **Context menu integration**: Right-click option to pick colors

## 🔧 Installation

### Method 1: Load Unpacked Extension (Developer Mode)

1. **Download & Extract**
   - Download the extension files
   - Extract the ZIP file to a folder on your computer

2. **Open Chrome Extensions**
   - Open Google Chrome
   - Go to `chrome://extensions/`
   - Or click the three dots menu → More Tools → Extensions

3. **Enable Developer Mode**
   - Toggle "Developer mode" in the top-right corner

4. **Load Extension**
   - Click "Load unpacked"
   - Select the extracted extension folder
   - The extension should now appear in your extensions list

5. **Pin Extension (Optional)**
   - Click the puzzle piece icon in Chrome toolbar
   - Pin the Color Picker extension for easy access

## 🎮 How to Use

### Basic Color Picking
1. Click the Color Picker extension icon in your toolbar
2. Click the "Pick Color" button
3. Move your cursor over any element on the webpage
4. See the real-time color preview with magnifying glass
5. Click on the desired color to select it

### Using the Color Dialog
- **Large Color Preview**: Click to copy HEX code
- **Format Buttons**: Click HEX, RGB, or HSL to copy in that format
- **Save Button**: Save the color to your collection

### Managing Saved Colors
- **View Saved Colors**: Open the extension popup to see your collection
- **Copy Saved Colors**: Click any saved color to copy its HEX code
- **Delete Colors**: Hover over a color and click the × button

### Dark Mode
- Toggle the switch in the top-right of the extension popup
- Preference is automatically saved

## 🎯 Keyboard Shortcuts
- **ESC**: Cancel color picking mode
- **Click**: Select color and open color dialog

## 🛠️ Technical Details

### Files Structure
```
color-picker-extension/
├── manifest.json          # Extension configuration
├── popup.html             # Extension popup HTML
├── popup.css              # Extension popup styles
├── popup.js               # Extension popup logic
├── content.js             # Content script for color picking
├── content.css            # Content script styles
├── background.js          # Background service worker
└── icons/                 # Extension icons
    ├── icon16.png
    ├── icon32.png
    ├── icon48.png
    └── icon128.png
```

### Technologies Used
- **Manifest V3**: Latest Chrome extension format
- **HTML5 & CSS3**: Modern web technologies
- **JavaScript ES6+**: Modern JavaScript features
- **Chrome APIs**: Extensions, Storage, Scripting APIs

### Permissions
- `activeTab`: Access to current tab for color picking
- `storage`: Save user preferences and color history
- `scripting`: Inject content scripts for color detection

## 🎨 Customization

The extension supports extensive customization:

### Theme Customization
- Built-in dark/light mode toggle
- CSS custom properties for easy color changes
- Responsive design that adapts to different screen sizes

### Color Format Support
- **HEX**: #FF5733 (default web format)
- **RGB**: rgb(255, 87, 51) (standard RGB values)
- **HSL**: hsl(9, 100%, 60%) (hue, saturation, lightness)

## 🔍 Troubleshooting

### Common Issues

**Extension not loading:**
- Make sure Developer mode is enabled
- Try reloading the extension from chrome://extensions/
- Check for any error messages in the extension details

**Color picking not working:**
- Refresh the webpage and try again
- Check if the website allows content scripts
- Some secure pages (chrome://, about:) may not allow extensions

**Colors not saving:**
- Check if Chrome has storage permissions
- Try clearing extension data and reloading

### Support
For issues or feature requests, please check the extension's feedback options.

## 📋 Version History

### v1.0 (Initial Release)
- ✅ Basic color picking functionality
- ✅ HEX, RGB, HSL format support
- ✅ Color saving and management
- ✅ Dark mode support
- ✅ Responsive design
- ✅ Real-time preview with magnifier
- ✅ One-click clipboard copying

## 🎉 Tips & Tricks

1. **Quick Access**: Pin the extension to toolbar for one-click access
2. **Bulk Color Picking**: Pick multiple colors in a session - they're all saved
3. **Format Switching**: Use different formats for different design tools
4. **Dark Mode**: Perfect for late-night design work
5. **Keyboard Control**: Use ESC to quickly cancel picking mode

Enjoy picking colors! 🎨✨
