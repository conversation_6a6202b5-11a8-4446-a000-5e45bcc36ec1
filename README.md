# 🎨 Color Picker Chrome Extension

A Chrome extension that allows you to pick colors from any webpage using the modern EyeDropper API. Colors are automatically copied to clipboard and saved for future reference.

## ✨ Features

- **Pick colors from any webpage** using the native EyeDropper API
- **Automatic clipboard copy** - picked colors are instantly copied
- **Color history** - all picked colors are saved and displayed
- **No duplicates** - prevents saving the same color twice
- **Click to copy** - click any saved color to copy it again
- **Modern UI** - clean, responsive design
- **Manifest V3** - uses the latest Chrome extension standards

## 🚀 Installation

### Method 1: Load as Unpacked Extension (Development)

1. Open Chrome and navigate to `chrome://extensions/`
2. Enable "Developer mode" in the top right corner
3. Click "Load unpacked" button
4. Select the folder containing this extension
5. The extension should now appear in your extensions list

### Method 2: Pack and Install

1. In Chrome, go to `chrome://extensions/`
2. Enable "Developer mode"
3. Click "Pack extension"
4. Select the extension folder
5. Install the generated `.crx` file

## 🎯 How to Use

1. **Click the extension icon** in your Chrome toolbar to open the popup
2. **Click "Pick Color from Page"** button
3. **Your cursor will change to a crosshair** - move it over any element
4. **Click on the color you want to pick**
5. **The color is automatically:**
   - Copied to your clipboard
   - Displayed in the popup
   - Saved to your color history
6. **View saved colors** in the popup - click any color to copy it again

## 🔧 Technical Details

### Files Structure
```
├── manifest.json          # Extension configuration
├── popup.html             # Extension popup interface
├── popup.js               # Popup logic and UI handling
├── content.js             # Content script for EyeDropper API
├── icons/                 # Extension icons
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
└── README.md              # This file
```

### Browser Compatibility
- **Chrome 95+** (EyeDropper API support)
- **Edge 95+** (EyeDropper API support)
- **Not supported in Firefox** (EyeDropper API not available)

### Permissions Used
- `activeTab` - To inject content script into current tab
- `storage` - To save picked colors locally

## 🛠️ Development

### Key Components

1. **Manifest V3 Configuration**
   - Uses modern Chrome extension standards
   - Content script injection for all URLs
   - Minimal permissions for security

2. **EyeDropper API Integration**
   - Native browser color picking
   - Handles user cancellation gracefully
   - Error handling for unsupported browsers

3. **Message Passing**
   - Popup ↔ Content Script communication
   - Async/await for clean code
   - Proper error propagation

4. **Storage Management**
   - Chrome storage API for persistence
   - Duplicate prevention
   - Automatic cleanup (50 color limit)

### Error Handling
- Browser compatibility checks
- User cancellation detection
- Permission error handling
- Graceful fallbacks for clipboard access

## 🎨 Customization

### Styling
The popup uses CSS custom properties and can be easily customized by modifying the styles in `popup.html`.

### Color Limit
Currently limited to 50 saved colors. Modify this in `popup.js`:
```javascript
// Change this value in the saveColor function
if (savedColors.length > 50) {
  savedColors = savedColors.slice(0, 50);
}
```

### Notification Duration
The on-page notification duration can be changed in `content.js`:
```javascript
// Change timeout duration (currently 3000ms = 3 seconds)
setTimeout(() => {
  // ... notification removal code
}, 3000);
```

## 🐛 Troubleshooting

### "EyeDropper API not supported"
- Ensure you're using Chrome 95+ or Edge 95+
- The EyeDropper API is not available in Firefox

### "Permission denied"
- Make sure the extension has access to the current tab
- Try refreshing the page and trying again

### Colors not saving
- Check if the extension has storage permissions
- Try clearing extension data and reloading

### Popup not opening
- Check if the extension is enabled
- Look for errors in the Chrome extension developer tools

## 📝 License

This project is open source and available under the MIT License.

## 🤝 Contributing

Feel free to submit issues, feature requests, or pull requests to improve this extension!
